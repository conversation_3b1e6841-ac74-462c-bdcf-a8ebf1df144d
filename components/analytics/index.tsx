import GoogleAnalytics from "./google-analytics";
import GoogleAdSense from "./google-adsense";
import OpenPanelAnalytics from "./open-panel";
import MicrosoftClarity from "./microsoft-clarity";

export default function Analytics() {
  if (process.env.NODE_ENV !== "production") {
    return null;
  }

  return (
    <>
      <GoogleAdSense />

      <OpenPanelAnalytics />

      <GoogleAnalytics />

      <MicrosoftClarity />
    </>
  );
}
