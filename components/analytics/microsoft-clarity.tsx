"use client";

import { useEffect } from 'react';
import Clarity from '@microsoft/clarity';

export default function MicrosoftClarity() {
  // 只在生产环境中运行
  if (process.env.NODE_ENV !== "production") {
    return null;
  }

  const clarityId = process.env.NEXT_PUBLIC_MICROSOFT_CLARITY_ID;

  // 如果没有配置Clarity ID，则不渲染
  if (!clarityId) {
    return null;
  }

  useEffect(() => {
    // 检查是否已经初始化过，避免重复初始化
    if (typeof window !== 'undefined' && !window.clarity) {
      // 初始化Microsoft Clarity
      Clarity.init(clarityId);

      console.log('Microsoft Clarity initialized with ID:', clarityId);
    }
  }, [clarityId]);

  return null;
}
