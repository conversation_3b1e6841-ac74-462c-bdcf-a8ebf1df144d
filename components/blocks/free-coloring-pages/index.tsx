"use client"

import { useState, useEffect } from "react"
import { useTranslations } from "next-intl"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"

import { Skeleton } from "@/components/ui/skeleton"
import { AspectRatio } from "@/components/ui/aspect-ratio"

import Image from "next/image"
import Link from "next/link"
import { coloringCategories } from "@/lib/coloring-categories"
import { ColoringPage } from "@/types/coloring-category"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

interface FreeColoringPagesProps {
  selectedCategory?: string
}

export default function FreeColoringPages({ selectedCategory }: FreeColoringPagesProps) {
  const t = useTranslations("free_coloring_pages")
  const [coloringPages, setColoringPages] = useState<ColoringPage[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const activeCategory = selectedCategory || "all"

  const itemsPerPage = 12

  const fetchColoringPages = async (category: string, page: number) => {
    setLoading(true)
    try {
      // 将"all"映射为"latest"用于API调用
      const apiCategory = category === "all" ? "latest" : category
      const response = await fetch(
        `/api/coloring-pages?category=${apiCategory}&page=${page}&limit=${itemsPerPage}`
      )

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setColoringPages(data.pages)
        setTotalPages(data.total_pages)
      } else {
        throw new Error(data.message || "Failed to fetch coloring pages")
      }
    } catch (error) {
      console.error("Error fetching coloring pages:", error)
      // 设置空数据以显示错误状态
      setColoringPages([])
      setTotalPages(1)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchColoringPages(activeCategory, currentPage)
  }, [activeCategory, currentPage])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <div className="container mx-auto px-4 py-8">

      <div className="flex flex-col lg:flex-row gap-8">
        {/* 左侧分类列表 */}
        <div className="lg:w-1/4">
          <div className="rounded-lg bg-background">
            <div className="p-4">
              <h2 className="text-lg font-semibold mb-3">{t("categories")}</h2>
              <div className="space-y-1">
                {/* All Categories */}
                <Link href="/free-coloring-pages">
                  <Button
                    variant="ghost"
                    className={`w-full justify-start text-sm ${
                      activeCategory === "all"
                        ? "shadow-md border border-primary/20 bg-primary/5 text-primary font-medium"
                        : "hover:shadow-sm hover:bg-muted"
                    }`}
                  >
                    {t("all_coloring_pages")}
                  </Button>
                </Link>

                {/* 分类列表 */}
                {coloringCategories.map((category) => (
                  <Link key={category.id} href={`/free-coloring-pages/${category.slug}`}>
                    <Button
                      variant="ghost"
                      className={`w-full justify-start text-sm ${
                        activeCategory === category.slug
                          ? "shadow-md border border-primary/20 bg-primary/5 text-primary font-medium"
                          : "hover:shadow-sm hover:bg-muted"
                      }`}
                    >
                      <span className="mr-2">{category.icon}</span>
                      {t(`coloring_categories.${category.id}.name`)}
                    </Button>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧内容区域 */}
        <div className="lg:w-3/4">
          {/* 标题 */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold">
              {activeCategory === "all"
                ? t("all_coloring_pages")
                : (() => {
                    const category = coloringCategories.find(c => c.slug === activeCategory);
                    return category ? t(`coloring_categories.${category.id}.name`) : activeCategory;
                  })()
              }
            </h1>
          </div>

          {/* 图片网格 */}
          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
              {Array.from({ length: itemsPerPage }).map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <AspectRatio ratio={1}>
                    <Skeleton className="w-full h-full" />
                  </AspectRatio>
                  <CardContent className="p-3">
                    <Skeleton className="h-4 w-3/4" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : coloringPages.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
              {coloringPages.map((page) => (
                <Card key={page.id} className="overflow-hidden group hover:shadow-lg transition-all duration-300 cursor-pointer">
                  <AspectRatio ratio={1}>
                    <Image
                      src={page.image_url}
                      alt={page.title}
                      fill
                      className="object-cover transition-transform group-hover:scale-105"
                      sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                    />
                  </AspectRatio>
                  <CardContent className="p-3">
                    <h3 className="font-medium text-sm truncate">{page.title}</h3>
                    <p className="text-xs text-muted-foreground mt-1">
                      {new Date(page.created_at).toLocaleDateString()}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-16 mb-8">
              <div className="text-6xl mb-4">🎨</div>
              <h3 className="text-xl font-semibold mb-2">{t("no_results")}</h3>
              <p className="text-muted-foreground text-center max-w-full">
                {activeCategory === "all"
                  ? t("no_results_all")
                  : (() => {
                      const categoryData = coloringCategories.find(c => c.slug === activeCategory);
                      const categoryName = categoryData?.name || activeCategory;
                      return t("no_results_category", { category: categoryName });
                    })()
                }
              </p>
            </div>
          )}

          {/* 分页 */}
          {!loading && totalPages > 1 && (
            <div className="flex flex-col items-center space-y-4">
              <div className="text-sm text-muted-foreground">
                {t("pagination_info").replace("{current}", currentPage.toString()).replace("{total}", totalPages.toString())}
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      href="#"
                      onClick={(e) => {
                        e.preventDefault()
                        if (currentPage > 1) handlePageChange(currentPage - 1)
                      }}
                      className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>

                  {/* 智能页码显示 */}
                  {(() => {
                    const pages = [];
                    const showPages = 5;
                    let startPage = Math.max(1, currentPage - Math.floor(showPages / 2));
                    let endPage = Math.min(totalPages, startPage + showPages - 1);

                    if (endPage - startPage + 1 < showPages) {
                      startPage = Math.max(1, endPage - showPages + 1);
                    }

                    for (let i = startPage; i <= endPage; i++) {
                      pages.push(
                        <PaginationItem key={i}>
                          <PaginationLink
                            href="#"
                            onClick={(e) => {
                              e.preventDefault()
                              handlePageChange(i)
                            }}
                            isActive={currentPage === i}
                          >
                            {i}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    }

                    return pages;
                  })()}

                  {currentPage < totalPages - 2 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      href="#"
                      onClick={(e) => {
                        e.preventDefault()
                        if (currentPage < totalPages) handlePageChange(currentPage + 1)
                      }}
                      className={currentPage >= totalPages ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
