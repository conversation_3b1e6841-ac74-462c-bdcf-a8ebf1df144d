import { ColoringCategory } from "@/types/coloring-category";

export const coloringCategories: ColoringCategory[] = [
  {
    id: "animal",
    name: "Animal",
    slug: "animals",
    description: "Cute and wild animals coloring pages",
    icon: "🐾",
    count: 0
  },
  {
    id: "events",
    name: "Events",
    slug: "events",
    description: "Special occasions and celebrations",
    icon: "🎉",
    count: 0
  },
  {
    id: "anime-manga",
    name: "Anime & Manga",
    slug: "anime-manga",
    description: "Japanese anime and manga characters",
    icon: "🎌",
    count: 0
  },
  {
    id: "music",
    name: "Music",
    slug: "music",
    description: "Musical instruments and notes",
    icon: "🎵",
    count: 0
  },
  {
    id: "prince-princess",
    name: "Prince & Princess",
    slug: "prince-princess",
    description: "Royal characters and fairy tales",
    icon: "👑",
    count: 0
  },
  {
    id: "places-culture",
    name: "Places & Culture",
    slug: "places-culture",
    description: "Famous places and cultural elements",
    icon: "🏛️",
    count: 0
  },
  {
    id: "education",
    name: "Education",
    slug: "education",
    description: "Learning and educational content",
    icon: "📚",
    count: 0
  },
  {
    id: "flowers-plants",
    name: "Flowers & Plants",
    slug: "flowers-plants",
    description: "Beautiful flowers and nature",
    icon: "🌸",
    count: 0
  },
  {
    id: "cartoon",
    name: "Cartoon",
    slug: "cartoon",
    description: "Cartoon characters and animations",
    icon: "🎨",
    count: 0
  },
  {
    id: "art",
    name: "Art",
    slug: "art",
    description: "Artistic patterns and designs",
    icon: "🎭",
    count: 0
  },
  {
    id: "landscape",
    name: "Landscape",
    slug: "landscape",
    description: "Beautiful landscapes and scenery",
    icon: "🏞️",
    count: 0
  },
  {
    id: "transportation",
    name: "Transportation",
    slug: "transportation",
    description: "Cars, planes, trains and more",
    icon: "🚗",
    count: 0
  },
  {
    id: "superheroes-villains",
    name: "Superheroes & Villains",
    slug: "superheroes-villains",
    description: "Comic book heroes and villains",
    icon: "🦸",
    count: 0
  },
  {
    id: "sports",
    name: "Sports",
    slug: "sports",
    description: "Various sports and activities",
    icon: "⚽",
    count: 0
  },
  {
    id: "people",
    name: "People",
    slug: "people",
    description: "People and professions",
    icon: "👥",
    count: 0
  },
  {
    id: "thematic",
    name: "Thematic",
    slug: "thematic",
    description: "Special themes and topics",
    icon: "🎯",
    count: 0
  }
];

export function getCategoryBySlug(slug: string): ColoringCategory | undefined {
  return coloringCategories.find(category => category.slug === slug);
}

export function getCategoryById(id: string): ColoringCategory | undefined {
  return coloringCategories.find(category => category.id === id);
}
