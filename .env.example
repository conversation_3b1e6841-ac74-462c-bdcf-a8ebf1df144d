# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME = "MakeColoring"

# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase URL and Anon Key
SUPABASE_URL = ""
SUPABASE_ANON_KEY = ""
SUPABASE_SERVICE_ROLE_KEY = ""

# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# -----------------------------------------------------------------------------
AUTH_SECRET = "Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0="

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = ""
AUTH_GOOGLE_SECRET = ""
NEXT_PUBLIC_AUTH_GOOGLE_ID = ""
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "false"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "false"

# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = ""

# -----------------------------------------------------------------------------
# Analytics with Microsoft Clarity
# https://clarity.microsoft.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_MICROSOFT_CLARITY_ID = ""

# -----------------------------------------------------------------------------
# Google AdSense
# https://www.google.com/adsense
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID = ""

# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""



# -----------------------------------------------------------------------------
# Payment with PayPal
# https://developer.paypal.com/docs/checkout/
# -----------------------------------------------------------------------------
PAYPAL_CLIENT_ID = ""
PAYPAL_CLIENT_SECRET = ""
PAYPAL_ENVIRONMENT = "sandbox"  # sandbox or production
PAYPAL_WEBHOOK_ID = ""

# PayPal订阅计划ID (需要在PayPal开发者控制台中创建)
# 月度计划
PAYPAL_BASIC_MONTHLY_PLAN_ID = ""
PAYPAL_PRO_MONTHLY_PLAN_ID = ""
PAYPAL_PREMIUM_MONTHLY_PLAN_ID = ""

# 年度计划
PAYPAL_BASIC_YEARLY_PLAN_ID = ""
PAYPAL_PRO_YEARLY_PLAN_ID = ""
PAYPAL_PREMIUM_YEARLY_PLAN_ID = ""

NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/pricing"

# -----------------------------------------------------------------------------
# API Callback URLs
# -----------------------------------------------------------------------------
# Kie.ai callback URL for async image generation
KIE_CALLBACK_URL = ""

# -----------------------------------------------------------------------------
# Internationalization Configuration
# -----------------------------------------------------------------------------
# Enable automatic locale detection based on browser settings
NEXT_PUBLIC_LOCALE_DETECTION = "true"

ADMIN_EMAILS = ""

NEXT_PUBLIC_DEFAULT_THEME = "light"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = ""
STORAGE_REGION = ""
STORAGE_ACCESS_KEY = ""
STORAGE_SECRET_KEY = ""
STORAGE_BUCKET = ""
STORAGE_DOMAIN = ""

# -----------------------------------------------------------------------------
# AI Image Generation Configuration
# -----------------------------------------------------------------------------
# 选择主要的图像生成provider: replicate | apicore | kie
ACTIVE_IMAGE_PROVIDER = "apicore"

# Provider API Keys - 填入你要使用的provider的API密钥即可自动启用
REPLICATE_API_TOKEN = ""
APICORE_API_TOKEN = ""
KIE_API_TOKEN = ""

# 默认积分消耗
DEFAULT_CREDITS_COST = "3"

# -----------------------------------------------------------------------------
# Email Service with Resend
# -----------------------------------------------------------------------------
# Get your API key from https://resend.com/api-keys
RESEND_API_KEY = ""

# -----------------------------------------------------------------------------
# Development Testing Configuration
# -----------------------------------------------------------------------------
# Set to 'true' to enable unlimited credits for testing in development mode
UNLIMITED_CREDITS_FOR_TESTING = "false"
# Set to 'true' to force non-premium status for testing premium features
FORCE_NON_PREMIUM = "false"

