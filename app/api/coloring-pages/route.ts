import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { ColoringPage, ColoringPagesResponse } from "@/types/coloring-category";
import { getCategoryBySlug } from "@/lib/coloring-categories";

// 模拟涂色页面数据
const generateMockColoringPages = (
  category: string, 
  page: number, 
  limit: number
): ColoringPagesResponse => {
  const mockPages: ColoringPage[] = [];
  const startId = (page - 1) * limit + 1;
  
  // 根据分类生成不同的图片
  const categoryImages: { [key: string]: string[] } = {
    latest: [
      "https://picsum.photos/400/400?random=1",
      "https://picsum.photos/400/400?random=2",
      "https://picsum.photos/400/400?random=3",
    ],
    animals: [
      "https://picsum.photos/400/400?random=10",
      "https://picsum.photos/400/400?random=11",
      "https://picsum.photos/400/400?random=12",
    ],
    flowers: [
      "https://picsum.photos/400/400?random=20",
      "https://picsum.photos/400/400?random=21",
      "https://picsum.photos/400/400?random=22",
    ],
    // 可以为其他分类添加更多图片
  };

  const images = categoryImages[category] || categoryImages.latest;
  
  for (let i = 0; i < limit; i++) {
    const imageIndex = (startId + i - 1) % images.length;
    mockPages.push({
      id: `${category}-${startId + i}`,
      title: `${category === 'latest' ? 'Latest' : category} Coloring Page ${startId + i}`,
      description: `Beautiful ${category} coloring page for all ages`,
      image_url: images[imageIndex],
      thumbnail_url: images[imageIndex],
      category_id: category === 'latest' ? undefined : category,
      created_at: new Date(Date.now() - (startId + i) * 24 * 60 * 60 * 1000).toISOString(),
      is_featured: i < 3
    });
  }

  const total = 120; // 模拟总数
  const totalPages = Math.ceil(total / limit);

  return {
    pages: mockPages,
    total,
    current_page: page,
    total_pages: totalPages,
    has_next: page < totalPages,
    has_prev: page > 1
  };
};

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const category = searchParams.get("category") || "latest";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");

    // 验证分类是否存在（除了latest）
    if (category !== "latest") {
      const categoryData = getCategoryBySlug(category);
      if (!categoryData) {
        return respErr("Category not found");
      }
    }

    // 验证分页参数
    if (page < 1 || limit < 1 || limit > 50) {
      return respErr("Invalid pagination parameters");
    }

    const response = generateMockColoringPages(category, page, limit);

    return respData({
      success: true,
      ...response
    });
  } catch (error) {
    console.error("Error fetching coloring pages:", error);
    return respErr("Failed to fetch coloring pages");
  }
}
