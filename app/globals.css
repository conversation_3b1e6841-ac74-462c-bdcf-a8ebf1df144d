@tailwind base;
@tailwind components;
@tailwind utilities;

@import "theme.css";

html {
  scroll-behavior: smooth;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  :root {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--background);
    --sidebar-accent-foreground: var(--primary);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }
  .dark {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }
}

@layer components {
  /* Fix navigation menu alignment */
  [data-radix-navigation-menu-viewport] {
    transform-origin: var(--radix-navigation-menu-content-transform-origin);
    left: var(--radix-navigation-menu-viewport-position-x, 0) !important;
  }
}
