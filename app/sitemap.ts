import { MetadataRoute } from 'next'
import { locales } from '@/i18n/locale'
import { coloringCategories } from '@/lib/coloring-categories'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://makecoloring.com'

  // 基础页面路径 - 只包含已开发的页面
  const routes = [
    '', // 首页
    '/pricing', // 定价页面
    '/privacy-policy', // 隐私政策
    '/terms-of-service', // 服务条款
    '/free-coloring-pages', // 免费涂色页面
    '/text-to-coloring-page', // 文字转涂色页面
    '/image-to-coloring-page', // 图片转涂色页面
  ]

  // 添加免费涂色页面分类路径
  const categoryRoutes = coloringCategories.map(category => `/free-coloring-pages/${category.slug}`)
  
  // 合并所有路径
  const allRoutes = [...routes, ...categoryRoutes]

  // 为每个语言和路径生成URL
  const sitemapEntries: MetadataRoute.Sitemap = []

  allRoutes.forEach(route => {
    locales.forEach(locale => {
      const url = locale === 'en'
        ? `${baseUrl}${route}`
        : `${baseUrl}/${locale}${route}`

      // 设置不同页面的优先级和更新频率
      let priority = 0.8
      let changeFrequency: 'daily' | 'weekly' | 'monthly' = 'weekly'

      if (route === '') {
        // 首页最高优先级
        priority = 1
        changeFrequency = 'daily'
      } else if (route === '/free-coloring-pages' || route.startsWith('/free-coloring-pages/')) {
        // 免费涂色页面高优先级
        priority = 0.9
        changeFrequency = 'weekly'
      } else if (route === '/text-to-coloring-page' || route === '/image-to-coloring-page') {
        // 主要功能页面高优先级
        priority = 0.9
        changeFrequency = 'weekly'
      } else if (route === '/pricing') {
        // 定价页面高优先级
        priority = 0.9
        changeFrequency = 'monthly'
      } else if (route === '/privacy-policy' || route === '/terms-of-service') {
        // 法律页面低优先级
        priority = 0.5
        changeFrequency = 'monthly'
      }

      sitemapEntries.push({
        url,
        lastModified: new Date(),
        changeFrequency,
        priority,
      })
    })
  })

  return sitemapEntries
}
