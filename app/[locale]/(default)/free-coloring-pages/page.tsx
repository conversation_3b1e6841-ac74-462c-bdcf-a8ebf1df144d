import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import FreeColoringPages from "@/components/blocks/free-coloring-pages";

export async function generateMetadata({
  params: { locale: _locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const t = await getTranslations("free_coloring_pages");

  return {
    title: t("title"),
    description: t("description"),
  };
}

export default function FreeColoringPagesPage() {
  return (
    <div className="min-h-screen bg-background">
      <FreeColoringPages />
    </div>
  );
}