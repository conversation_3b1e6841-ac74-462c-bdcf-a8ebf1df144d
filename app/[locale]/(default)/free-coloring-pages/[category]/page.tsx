import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import FreeColoringPages from "@/components/blocks/free-coloring-pages";
import { getCategoryBySlug } from "@/lib/coloring-categories";

interface CategoryPageProps {
  params: {
    locale: string;
    category: string;
  };
}

export async function generateMetadata({
  params: { locale: _locale, category },
}: CategoryPageProps): Promise<Metadata> {
  const t = await getTranslations("free_coloring_pages");
  const categoryData = getCategoryBySlug(category);

  if (!categoryData) {
    return {
      title: "Category Not Found",
      description: "The requested coloring page category was not found.",
    };
  }

  return {
    title: `${categoryData.name} ${t("title")}`,
    description: categoryData.description || t("description"),
  };
}

export default function CategoryPage({ params }: CategoryPageProps) {
  const { category } = params;
  const categoryData = getCategoryBySlug(category);

  if (!categoryData) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-background">
      <FreeColoringPages selectedCategory={category} />
    </div>
  );
}
