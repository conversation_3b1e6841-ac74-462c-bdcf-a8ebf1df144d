export interface ColoringCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  image?: string;
  count?: number;
}

export interface ColoringPage {
  id: string;
  title: string;
  description?: string;
  image_url: string;
  thumbnail_url?: string;
  category_id?: string;
  tags?: string[];
  created_at: string;
  updated_at?: string;
  is_featured?: boolean;
  download_count?: number;
}

export interface ColoringPagesResponse {
  pages: ColoringPage[];
  total: number;
  current_page: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}
